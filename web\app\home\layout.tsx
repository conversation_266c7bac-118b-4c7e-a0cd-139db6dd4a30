'use client'
import Sidebar from './Sidebar'
import { createContext, useContext, useEffect, useState } from 'react'

import useSWR from 'swr'
// import Avatar from '@/app/components/base/avatar'
import decorate from '@/assets/images/layout/decorate.png'
import CustomDialog from '@/app/components/base/dialog'
import LoginPage from '@/app/home/<USER>/page'
import { fetchUserProfile } from '@/service/common'
import type { UserProfileResponse } from '@/models/common'

// 声明window扩展属性用于全局登录弹窗
declare global {
  interface Window {
    showHomeLoginDialog?: () => void
  }
}

// 创建 HomeContext
type HomeContextType = {
  isLogin: boolean
  showLoginDialog: () => void
  reloadData: () => void
  userProfile: UserProfileResponse | null
}

const HomeContext = createContext<HomeContextType | null>(null)

export const useHomeContext = () => {
  const context = useContext(HomeContext)
  if (!context)
    throw new Error('无用户信息')
  return context
}

function LayoutContent({ children }: { children: React.ReactNode }) {
  // 使用fetchUserProfile接口获取用户信息
  const { data: userProfileResponse, mutate: mutateUserProfile, error: profileError, isLoading } = useSWR(
    { url: '/account/profile', params: {} },
    fetchUserProfile,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      shouldRetryOnError: false,
      dedupingInterval: 1000, // 减少去重间隔，允许更频繁的重新验证
      // 立即开始请求，不等待
      suspense: false,
      // 添加错误重试配置
      errorRetryCount: 0,
      errorRetryInterval: 1000,
    },
  )

  // 处理用户信息
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null)
  const [isLogin, setIsLogin] = useState(false)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)

  // 保存用户信息
  useEffect(() => {
    const processUserProfile = async () => {
      // 如果正在加载，等待
      if (isLoading)
        return

      // 标记已初始化
      if (!hasInitialized)
        setHasInitialized(true)

      // 如果有错误（比如401未登录），设置为未登录状态
      if (profileError) {
        console.log('err', profileError)
        setUserProfile(null)
        setIsLogin(false)
        return
      }

      // 如果有响应数据，处理用户信息
      if (userProfileResponse) {
        try {
          const profile: UserProfileResponse = await userProfileResponse.json()
          console.log('用户信息', profile)
          setUserProfile(profile)
          // console.log(!!profile?.id)
          setIsLogin(!!profile?.id)
        }
        catch (error) {
          console.warn('err', error)
          setUserProfile(null)
          setIsLogin(false)
        }
      }
      else if (hasInitialized) {
        console.log('hasInitialized', hasInitialized)
        // 如果已经初始化但没有数据，说明未登录
        setUserProfile(null)
        setIsLogin(false)
      }
    }

    processUserProfile()
  }, [userProfileResponse, profileError, isLoading, hasInitialized])

  const showLoginDialogHandler = () => setShowLoginDialog(true)

  // 数据重新加载方法
  const reloadData = async () => {
    // 重新获取用户信息，强制刷新缓存
    await mutateUserProfile(undefined, { revalidate: true })
    // 触发全局数据重新加载事件
    window.dispatchEvent(new CustomEvent('homeDataReload'))
  }

  // 全局挂载弹窗触发方法
  useEffect(() => {
    window.showHomeLoginDialog = showLoginDialogHandler
    return () => {
      delete window.showHomeLoginDialog
    }
  }, [])

  // 监听 token 变化，当 token 存在时立即重新验证用户信息
  useEffect(() => {
    const checkTokenAndRevalidate = () => {
      const token = localStorage.getItem('console_token')
      if (token && !isLogin && !isLoading) {
        // 如果有 token 但当前状态是未登录，立即重新验证
        mutateUserProfile(undefined, { revalidate: true })
      }
    }

    // 立即检查一次
    checkTokenAndRevalidate()

    // 监听 storage 变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'console_token')
        checkTokenAndRevalidate()
    }

    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [isLogin, isLoading, mutateUserProfile])

  const homeContextValue = {
    isLogin,
    showLoginDialog: showLoginDialogHandler,
    reloadData,
    userProfile,
  }

  return (
    <HomeContext.Provider value={homeContextValue}>
      <div className="flex min-h-screen w-full overflow-x-auto bg-[#F3F4F6] font-[PingFangSC]">
        {/* 侧边栏 */}
        <div className="shrink-0">
          <Sidebar />
        </div>
        {/* 主体内容 */}
        <main className="relative flex h-screen min-w-0 flex-1 flex-col items-center overflow-auto rounded-[11px] border-[1px] border-[#FFFFFF]" style={{ background: 'linear-gradient(180deg, #ECF4FF 0%, #FFFFFF 30%, #FFFFFF 100%)' }}>
          <img src={decorate.src} alt="decorateIcon" className='absolute right-[285px] top-[35px] hidden lg:block' />
          {children}
          {/* 全局登录弹窗 */}
          <CustomDialog
            show={showLoginDialog}
            onClose={() => setShowLoginDialog(false)}
            className="relative p-0"
            maskClassName="!bg-black !bg-opacity-60"
          >
            <LoginPage
              onSuccess={async () => {
                // 登录成功后重新加载数据
                await reloadData()
                // 确保登录状态立即更新
                setIsLogin(true)
                setShowLoginDialog(false)
              }}
            />
            <button
              className="absolute right-[20px] top-[15px] size-[20px] text-[20px] text-[#212121]"
              onClick={() => setShowLoginDialog(false)}
            >
              ×
            </button>
          </CustomDialog>
        </main>
      </div>
    </HomeContext.Provider>
  )
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <LayoutContent>{children}</LayoutContent>
  )
}
