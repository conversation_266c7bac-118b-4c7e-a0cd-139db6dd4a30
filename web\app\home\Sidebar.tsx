import React from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { fetchConversations } from '@/service/share'
import type { ConversationItem } from '@/models/share'
import { Divider } from 'antd'
import { HistoryOutlined } from '@ant-design/icons'
import { COMMON } from './common'
import { useDataReload } from '@/app/home/<USER>/useDataReload'
import home from '@/assets/images/sidebar/home.png'
import homeActive from '@/assets/images/sidebar/homeActive.png'
import appCenter from '@/assets/images/sidebar/appCenter.png'
import appCenterActive from '@/assets/images/sidebar/appCenterActive.png'
import knowledge from '@/assets/images/sidebar/knowledge.png'
import knowledgeActive from '@/assets/images/sidebar/knowledgeActive.png'
import material from '@/assets/images/sidebar/material.png'
import materialActive from '@/assets/images/sidebar/materialActive.png'
import search from '@/assets/images/sidebar/search.png'
import searchActive from '@/assets/images/sidebar/searchActive.png'
import LOGO from '@/assets/images/sidebar/LOGO.png'

const menus = [
  { key: 'HomePage', label: '首页', icon: home, activeIcon: homeActive, path: '/home' },
  { key: 'AppCenterPage', label: '应用中心', icon: appCenter, activeIcon: appCenterActive, path: '/home/<USER>' },
  { key: 'SearchPage', label: '智能学习搜索', icon: search, activeIcon: searchActive, path: '/home/<USER>' },
  { key: 'KnowledgePage', label: '知识库', icon: knowledge, activeIcon: knowledgeActive, path: '/home/<USER>' },
  { key: 'MaterialPage', label: '素材库', icon: material, activeIcon: materialActive, path: '/home/<USER>' },
]

// 历史记录
function SimpleChatHistory() {
  const [history, setHistory] = useState<ConversationItem[]>([])
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const selectedConversationId = searchParams.get('conversationId')

  const loadHistoryData = () => {
    fetchConversations(true, COMMON.appId, undefined, false, 10).then((res) => {
      setHistory(res.data || [])
    }).catch((err) => {
      if (err.isHomePageUnauth && window.showHomeLoginDialog)
        window.showHomeLoginDialog()
    })
  }

  useEffect(() => {
    loadHistoryData()
  }, []) // 移除selectedKey依赖

  // 监听登录成功后的数据重新加载事件
  useDataReload(loadHistoryData)

  // 始终在顶部插入新建项
  const newChatItem: ConversationItem = { id: '', name: '最新对话', inputs: {}, introduction: '' }
  const displayHistory = [newChatItem, ...history]

  const isChatPage = pathname.startsWith(`/home/<USER>/${COMMON.appId}`)

  return (
    <ul className="mb-2 truncate">
      {displayHistory.map((item, index) => {
        const isSelected = selectedConversationId ? selectedConversationId === item.id : (isChatPage && item.id === '')
        return (
          <li
            key={index}
            className={
              `font-[PingFang SC] cursor-pointer truncate rounded px-2 text-[12px] leading-[34px] ${isSelected ? 'rounded-[11px] bg-[#FFFFFF] font-[500] text-[#3C3C3C]' : 'text-[#7C7D7E]'} `
            }
            onClick={() => {
              if (item.id === '') {
                // 跳转到新建对话页（不带 conversationId 参数）
                router.push(`/home/<USER>/${COMMON.appId}`)
              }
              else {
                router.push(`/home/<USER>/${COMMON.appId}?conversationId=${item.id}`)
              }
            }}
          >
            {item.name || '未命名会话'}
          </li>
        )
      })}
    </ul>
  )
}

export default function Sidebar({
  onSelect,
}: {
  onSelect?: (key: string) => void;
}) {
  const router = useRouter()
  const pathname = usePathname()

  // 根据当前路径确定激活的菜单项
  const getActiveMenuKey = () => {
    // 特殊处理：如果是聊天页面，不激活任何主菜单项
    if (pathname.startsWith(`/home/<USER>/${COMMON.appId}`))
      return null

    // 查找匹配的菜单项
    const activeMenu = menus.find((menu) => {
      if (menu.key === 'HomePage')
        return pathname === '/home'
      return pathname.startsWith(menu.path)
    })

    return activeMenu?.key || null
  }

  const activeMenuKey = getActiveMenuKey()

  return (
    <aside className="flex w-56 min-w-[224px] flex-col px-[12px] py-6 text-left font-[PingFangSC] text-[14px] font-[500] leading-[40px] text-[#181818]">
        <div className="mb-6">
          <ul className="space-y-2">
            <li className='flex items-center gap-2 pl-[11px] text-[18px] font-[400] leading-[40px] text-[#000000]'>
              <img src={LOGO.src} alt="LOGO" className='size-[24px]'/>
              {COMMON.sidebarTitle}
            </li>
            {menus.map((menu, index) => {
              const isActive = activeMenuKey === menu.key
              return (
                <div key={index} className={`${isActive ? 'rounded-[11px] border-[#C7D7F9] bg-[#E6EBF7] text-[#0266FF]' : ''} flex items-center gap-2 pl-[11px]`}>
                  {
                    isActive ? <img src={menu.activeIcon.src} alt={menu.label} className='h-[11px] w-[11px]' /> : <img src={menu.icon.src} alt={menu.label} className='h-[11px] w-[11px]' />
                  }
                  <li
                  key={index}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    onSelect && onSelect(menu.key)
                    router.push(menu.path)
                  }}
                  >

                  {menu.label}
                </li>
              </div>
              )
            })}
          </ul>
        </div>
        <Divider />
       <div className='flex flex-col justify-center px-5'>
         <div className="flex justify-between text-[14px] font-[400] leading-[40px] text-[#181818]">
            <span className='flex gap-1'><HistoryOutlined />历史记录</span>
            <span className='cursor-pointer text-[12px] text-[#7C7D7E]' onClick={() => router.push('/home/<USER>')}>更多</span>
         </div>
         <div className="flex flex-col gap-2">
          <SimpleChatHistory />
         </div>
       </div>
    </aside>
  )
}
